#!/usr/bin/env python3
"""
CUDA-Optimized Fabric Defect Detection (threaded) with reduced false positives and target ~30 FPS display.

Usage:
    python cuda_fabric_detector.py --mode camera --model path/to/best.pt --confidence 0.4

Notes:
 - Requires torch + torchvision and OpenCV. For YOLOv5 via torch.hub we use ultralytics/yolov5 hub.
 - If CUDA is available the code will use FP16 and autocast for faster inference.
"""

import os
import time
import argparse
import logging
import json
import csv
from datetime import datetime
from threading import Thread, Lock
from queue import Queue, Empty
from collections import defaultdict, deque

import cv2
import numpy as np
import torch

# ---------------------------
# Utilities
# ---------------------------
def iou(boxA, boxB):
    # boxes: (x1,y1,x2,y2)
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])
    interW = max(0, xB - xA)
    interH = max(0, yB - yA)
    interArea = interW * interH
    boxAArea = max(1, (boxA[2] - boxA[0]) * (boxA[3] - boxA[1]))
    boxBArea = max(1, (boxB[2] - boxB[0]) * (boxB[3] - boxB[1]))
    return interArea / (boxAArea + boxBArea - interArea + 1e-9)

# ---------------------------
# Defect Logger (improved)
# ---------------------------
class DefectLogger:
    def __init__(self, log_dir="defect_logs"):
        self.log_dir = log_dir
        os.makedirs(self.log_dir, exist_ok=True)
        self._setup_logging()
        self.defect_counts = defaultdict(int)
        self.total_defects = 0
        self.session_start = datetime.now()
        self.defect_history = deque(maxlen=2000)  # last detections
        self.frame_count = 0
        self.lock = Lock()
        self.csv_file = os.path.join(self.log_dir, f"defects_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
        self._init_csv()
        self.logger.info("DefectLogger initialized")

    def _setup_logging(self):
        log_file = os.path.join(self.log_dir, f"detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(levelname)s - %(message)s',
                            handlers=[logging.FileHandler(log_file), logging.StreamHandler()])
        self.logger = logging.getLogger("DefectLogger")

    def _init_csv(self):
        with open(self.csv_file, "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(['timestamp', 'frame', 'class', 'confidence', 'x1','y1','x2','y2','area','session_total'])

    def log_defect(self, defect_type, confidence, bbox, frame_number):
        with self.lock:
            self.defect_counts[defect_type] += 1
            self.total_defects += 1
            self.frame_count = frame_number
            x1,y1,x2,y2 = bbox
            area = (x2-x1) * (y2-y1)
            rec = {
                'timestamp': datetime.now().isoformat(),
                'frame': frame_number,
                'type': defect_type,
                'confidence': float(confidence),
                'bbox': bbox,
                'area': area
            }
            self.defect_history.append(rec)
            self.logger.info(f"DEFECT: frame={frame_number} class={defect_type} conf={confidence:.3f} bbox={bbox} area={area}")
            # CSV
            try:
                with open(self.csv_file, "a", newline="") as f:
                    writer = csv.writer(f)
                    writer.writerow([rec['timestamp'], rec['frame'], rec['type'], rec['confidence'],
                                     x1,y1,x2,y2, area, self.total_defects])
            except Exception as e:
                self.logger.error(f"CSV write error: {e}")

    def get_stats(self):
        with self.lock:
            duration = (datetime.now() - self.session_start).total_seconds()
            defects_per_min = self.total_defects / max(duration/60.0, 1e-6)
            return {
                'total_defects': self.total_defects,
                'defect_counts': dict(self.defect_counts),
                'frames_processed': self.frame_count,
                'session_duration': str(datetime.now() - self.session_start),
                'defects_per_minute': defects_per_min
            }

    def save_session_summary(self):
        stats = self.get_stats()
        out = os.path.join(self.log_dir, f"session_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        try:
            with open(out, "w") as f:
                json.dump(stats, f, indent=2)
            self.logger.info(f"Session summary saved: {out}")
        except Exception as e:
            self.logger.error(f"Save summary failed: {e}")
        return out

# ---------------------------
# Detector (thread-safe, faster)
# ---------------------------
class CUDAFabricDetector:
    def __init__(self, model_path, confidence_threshold=0.4, use_cuda=True):
        self.confidence_threshold = confidence_threshold
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.device = torch.device("cuda" if self.use_cuda else "cpu")
        self._load_model(model_path)

        # filtering params
        self.min_defect_area = 40
        self.max_defect_area = 200000
        # temporal persistence: detection must be seen in at least N frames within window_size frames
        self.persistence_required = 2
        self.persistence_window = 3
        # history of detection tracks: list of recent detection records (deque)
        self.recent_detections = deque(maxlen=30)  # each entry: list of detections for a frame

        # per-class minimum confidence (can bump unknown classes higher)
        self.class_conf_map = defaultdict(lambda: 0.7)  # default for unknown classes
        # explicitly set known fabric classes to lower thresholds (accept easier)
        known = ['hole','stain','tear','wrinkle','defect','damage','spot','mark','flaw','thread','yarn',
                 'broken_thread','oil_stain','dirt','scratch','cut','burn','discoloration']
        for k in known:
            self.class_conf_map[k] = 0.35

        # blacklist (explicit non-fabric classes)
        self.non_fabric_tokens = set(['person','face','hand','finger','eye','nose','mouth','hair','body','arm','leg','head','human','person'])

        # logger
        self.defect_logger = DefectLogger()
        self.frame_count = 0

    def _load_model(self, model_path):
        print(f"Loading model from {model_path} on {self.device}")
        # Load yolov5 via hub (ultralytics)
        # Note: user originally used Torch Hub; keep same interface to maintain names attribute.
        self.model = torch.hub.load('ultralytics/yolov5', 'custom', path=model_path, verbose=False)
        self.model.to(self.device)
        self.model.eval()
        if self.use_cuda:
            try:
                self.model.half()
            except Exception:
                pass
        # set defaults
        self.model.conf = self.confidence_threshold
        self.model.iou = 0.45
        self.model.max_det = 30
        print("Model loaded; device:", self.device)

    def _is_fabric_defect(self, class_name, confidence):
        name = class_name.lower()
        # immediate reject if contains non-fabric token
        for token in self.non_fabric_tokens:
            if token in name:
                return False
        # if it's one of known defects, check against lower per-class threshold
        for kc, v in self.class_conf_map.items():
            if kc in name:
                return confidence >= v
        # unknown => require high confidence
        return confidence >= 0.75

    def _filter_and_persist(self, detections):
        """
        detections: list of dicts {'bbox':(x1,y1,x2,y2), 'confidence':float, 'class':str}
        Apply area/class filters, then apply temporal persistence across recent frames.
        Returns final list of persistent detections.
        """
        # area+class filtering first
        filtered = []
        for d in detections:
            x1,y1,x2,y2 = d['bbox']
            area = (x2-x1)*(y2-y1)
            if area < self.min_defect_area or area > self.max_defect_area:
                continue
            if not self._is_fabric_defect(d['class'], d['confidence']):
                continue
            filtered.append(d)

        # add to recent history
        self.recent_detections.append(filtered)

        # persistence check: for each candidate in current filtered, see how many frames within last window have IoU > 0.4 with similar class
        final = []
        window = list(self.recent_detections)[-self.persistence_window:]
        for cand in filtered:
            hits = 0
            for frame_det in window:
                for other in frame_det:
                    if cand['class'] == other['class'] and iou(cand['bbox'], other['bbox']) > 0.4:
                        hits += 1
                        break
            if hits >= self.persistence_required:
                final.append(cand)
        return final

    def detect(self, frame):
        """
        Run inference on a frame (numpy BGR).
        Returns drawn_frame (BGR) and list of final persistent detections.
        """
        self.frame_count += 1
        h, w = frame.shape[:2]
        # we pass the frame directly (yolov5 will handle resizing)
        try:
            with torch.no_grad():
                if self.use_cuda:
                    # use autocast for faster half precision inference
                    with torch.cuda.amp.autocast():
                        results = self.model(frame, size=640)
                else:
                    results = self.model(frame, size=640)

            detections = []
            # results.xyxy[0] contains detections for the frame
            if hasattr(results, 'xyxy') and len(results.xyxy) > 0:
                for det in results.xyxy[0]:
                    x1, y1, x2, y2, conf, cls = det
                    conf = float(conf)
                    if conf < 0.01:
                        continue
                    # scale coords are already in frame coords from model
                    x1, y1, x2, y2 = int(x1.item()), int(y1.item()), int(x2.item()), int(y2.item())
                    class_id = int(cls.item())
                    class_name = self.model.names.get(class_id, f"obj_{class_id}")
                    detections.append({'bbox': (x1,y1,x2,y2), 'confidence': conf, 'class': class_name, 'class_id': class_id})

            # filter + temporal persistence
            persistent = self._filter_and_persist(detections)

            # log persistent detections (only newly logged ones)
            for d in persistent:
                # avoid duplicate logging by checking last few history records
                # simple heuristic: only log if not logged in last 2 detector.defect_history entries with IoU > 0.5
                already = False
                for past in list(self.defect_logger.defect_history)[-6:]:
                    if d['class'] == past['type'] and iou(d['bbox'], past['bbox']) > 0.6:
                        already = True
                        break
                if not already:
                    self.defect_logger.log_defect(d['class'], d['confidence'], d['bbox'], self.frame_count)

            # draw on frame
            draw = frame.copy()
            for d in persistent:
                x1,y1,x2,y2 = d['bbox']
                conf = d['confidence']
                cname = d['class']
                # color logic
                color = (0,255,0)
                if 'hole' in cname.lower() or conf>0.85:
                    color = (0,0,255)
                elif 'stain' in cname.lower() or 0.7<conf<=0.85:
                    color = (0,165,255)
                thickness = 2 if conf < 0.7 else 3
                cv2.rectangle(draw, (x1,y1), (x2,y2), color, thickness)
                label = f"{cname}:{conf:.2f}"
                tsize = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(draw, (x1, y1 - tsize[1] - 8), (x1 + tsize[0], y1), color, -1)
                cv2.putText(draw, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 2)

            return draw, persistent
        except Exception as e:
            print("Detection error:", e)
            return frame, []

# ---------------------------
# Threaded Camera + Inference + Display
# ---------------------------
class CameraCapture(Thread):
    def __init__(self, src=0, width=1280, height=720, target_fps=30):
        super().__init__(daemon=True)
        self.src = src
        self.width = width
        self.height = height
        self.target_fps = target_fps
        self.cap = cv2.VideoCapture(self.src)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
        # attempt to set FPS (may be ignored depending on camera)
        self.cap.set(cv2.CAP_PROP_FPS, self.target_fps)
        self.queue = Queue(maxsize=2)
        self.running = True

    def run(self):
        while self.running:
            ret, frame = self.cap.read()
            if not ret:
                time.sleep(0.01)
                continue
            # drop if queue is full (we always keep latest)
            try:
                # if full, remove one and put new (keeps latest)
                if self.queue.full():
                    try:
                        _ = self.queue.get_nowait()
                    except Exception:
                        pass
                self.queue.put_nowait(frame)
            except Exception:
                pass
            # small sleep to avoid busy loop, but allow high rate
            time.sleep(0.001)

    def read(self, timeout=0.05):
        try:
            return self.queue.get(timeout=timeout)
        except Empty:
            return None

    def stop(self):
        self.running = False
        try:
            self.cap.release()
        except Exception:
            pass

class InferenceThread(Thread):
    def __init__(self, detector: CUDAFabricDetector, in_queue: CameraCapture):
        super().__init__(daemon=True)
        self.detector = detector
        self.in_queue = in_queue
        self.out_frame = None
        self.out_dets = []
        self.running = True
        self.lock = Lock()
        self.last_infer_time = 0.0
        self.estimated_fps = 0.0
        self.counter = 0
        self.timer = time.time()

    def run(self):
        while self.running:
            frame = self.in_queue.read()
            if frame is None:
                time.sleep(0.005)
                continue
            # always infer the latest frame available
            start = time.time()
            draw, dets = self.detector.detect(frame)
            elapsed = time.time() - start
            # update fps estimate
            self.counter += 1
            if time.time() - self.timer >= 1.0:
                self.estimated_fps = self.counter / (time.time() - self.timer)
                self.counter = 0
                self.timer = time.time()
            with self.lock:
                self.out_frame = draw
                self.out_dets = dets
                self.last_infer_time = elapsed

    def read(self):
        with self.lock:
            return self.out_frame, list(self.out_dets), self.last_infer_time, self.estimated_fps

    def stop(self):
        self.running = False

# ---------------------------
# Main - Display loop and controls
# ---------------------------
def cuda_camera_detection(model_path, confidence=0.9, camera_src=0):
    detector = CUDAFabricDetector(model_path, confidence_threshold=confidence, use_cuda=True)
    capt = CameraCapture(src=camera_src, width=1280, height=720, target_fps=30)
    inf = InferenceThread(detector, capt)

    capt.start()
    inf.start()

    window_name = "Fabric Defect Detection (Press q to quit)"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    last_show = None

    print("Controls: q=quit  s=save frame  +=inc conf  -=dec conf  r=reset counts  p=print stats")
    try:
        while True:
            out_frame, out_dets, last_infer_time, infer_fps = inf.read()
            if out_frame is None:
                # nothing ready yet, try to show last frame or black
                if last_show is None:
                    blank = np.zeros((480,640,3),dtype=np.uint8)
                    cv2.imshow(window_name, blank)
                else:
                    cv2.imshow(window_name, last_show)
                key = cv2.waitKey(1) & 0xFF
            else:
                # overlay stats
                stats = detector.defect_logger.get_stats()
                overlay = out_frame.copy()
                # simple semi-transparent panel
                cv2.rectangle(overlay, (10,10), (380,160), (0,0,0), -1)
                cv2.addWeighted(overlay, 0.5, out_frame, 0.5, 0, out_frame)
                cv2.putText(out_frame, f"FPS(inf): {infer_fps:.1f} inf_time:{last_infer_time*1000:.0f}ms", (20,35), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,255,0), 2)
                cv2.putText(out_frame, f"TotalDefects: {stats['total_defects']}  Def/min: {stats['defects_per_minute']:.1f}", (20,60), cv2.FONT_HERSHEY_SIMPLEX, 0.55, (255,255,0), 1)
                cv2.putText(out_frame, f"ConfThreshold: {detector.confidence_threshold:.2f}", (20,85), cv2.FONT_HERSHEY_SIMPLEX, 0.55, (200,200,200), 1)
                last_show = out_frame
                cv2.imshow(window_name, out_frame)
                key = cv2.waitKey(1) & 0xFF

            if key == ord('q') or key == 27:
                break
            elif key == ord('s'):
                if last_show is not None:
                    fname = f"frame_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                    cv2.imwrite(fname, last_show)
                    detector.defect_logger.logger.info(f"Saved frame: {fname}")
                    print("Saved:", fname)
            elif key == ord('+'):
                detector.confidence_threshold = min(0.95, detector.confidence_threshold + 0.05)
                detector.model.conf = detector.confidence_threshold
                print("Confidence ->", detector.confidence_threshold)
            elif key == ord('-'):
                detector.confidence_threshold = max(0.05, detector.confidence_threshold - 0.05)
                detector.model.conf = detector.confidence_threshold
                print("Confidence ->", detector.confidence_threshold)
            elif key == ord('r'):
                detector.defect_logger.defect_counts.clear()
                detector.defect_logger.total_defects = 0
                detector.defect_logger.defect_history.clear()
                print("Counts reset")
            elif key == ord('p'):
                s = detector.defect_logger.get_stats()
                print("="*40)
                print("STATS:")
                print(f"Total defects: {s['total_defects']}")
                print(f"Frames processed: {s['frames_processed']}")
                print(f"Session duration: {s['session_duration']}")
                for k,v in s['defect_counts'].items():
                    print(f"  {k}: {v}")
                print("="*40)

    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        print("Shutting down...")
        inf.stop()
        capt.stop()
        cv2.destroyAllWindows()
        summary = detector.defect_logger.save_session_summary()
        stats = detector.defect_logger.get_stats()
        print("Final stats:", stats)
        print("Summary saved to:", summary)

# ---------------------------
# Image mode (single image)
# ---------------------------
def cuda_image_detection(image_path, model_path, confidence=0.9, output_path=None):
    if not os.path.exists(image_path):
        print("Image not found:", image_path)
        return
    img = cv2.imread(image_path)
    if img is None:
        print("Cannot read image:", image_path)
        return
    detector = CUDAFabricDetector(model_path, confidence_threshold=confidence, use_cuda=True)
    out, dets = detector.detect(img)
    out = detector.defect_logger  # we used logger inside detector, but drawing already done
    if output_path:
        cv2.imwrite(output_path, out)
        print("Saved:", output_path)
    else:
        cv2.imshow("Result", out)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    detector.defect_logger.save_session_summary()

# ---------------------------
# CLI
# ---------------------------
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", choices=["camera","image"], default="camera")
    parser.add_argument("--image", "-i", help="image path")
    parser.add_argument("--model", "-m", required=True, help="path to model (.pt)")
    parser.add_argument("--confidence", "-c", type=float, default=0.4)
    parser.add_argument("--cpu", action="store_true", help="force CPU")
    parser.add_argument("--src", type=int, default=0, help="camera source index (default 0)")
    args = parser.parse_args()

    # device messages
    if not args.cpu and torch.cuda.is_available():
        print("CUDA available. Using GPU.")
    elif not args.cpu:
        print("CUDA NOT available. Using CPU.")
    else:
        print("Forcing CPU mode.")

    try:
        if args.mode == "camera":
            cuda_camera_detection(args.model, args.confidence, camera_src=args.src)
        else:
            if not args.image:
                print("Image mode requires --image")
                return
            cuda_image_detection(args.image, args.model, args.confidence)
    except Exception as e:
        print("Fatal error:", e)
        import traceback; traceback.print_exc()

if __name__ == "__main__":
    main()
